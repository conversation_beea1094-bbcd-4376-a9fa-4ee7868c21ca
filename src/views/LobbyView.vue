<script setup lang="ts">
import { ref } from "vue";
import { useDisableZoom } from "@/composables/useDisableZoom.ts";

import InitScene from "@/components/InitScene/index.vue";
import CanvasLayout from "@/layout/CanvasLayout.vue";
import LayoutHeader from "@/components/LayoutHeader/index.vue";
import LayoutBody from "@/components/LayoutBody/index.vue";
import LayoutFooter from "@/components/LayoutFooter/index.vue";
import WinAnim from "@/components/WinAnim/index.vue";

import NumPanel from "@/components/NumPanel/index.vue"
import TabsPanel from "@/components/TabsPanel/index.vue";

useDisableZoom();

const isFinish = ref(false);
</script>

<template>
  <CanvasLayout>
    <div id="view-box">
      <InitScene v-if="!isFinish" @finish="isFinish = true" />
      
      <template v-else>
        <LayoutHeader />

        <LayoutBody>
          <NumPanel class="mb-4" />
          <TabsPanel />
        </LayoutBody>

        <LayoutFooter />
      </template>

      <WinAnim />
    </div>
  </CanvasLayout>
</template>

<style scoped lang="scss">
#view-box {
  width: 100%;
  height: 100%;
  position: relative;
  overscroll-behavior: none;
  display: flex;
  flex-direction: column;
}
</style>