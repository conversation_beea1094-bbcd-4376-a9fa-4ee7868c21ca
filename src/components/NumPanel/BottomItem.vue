<script setup lang="ts">
import {computed} from "vue";

const {
  type = 'Big',
  info = {
    num1: 0,
    num2: 0
  }
} = defineProps<{
  type: 'Big' | 'Small'
  info?: {
    num1: number,
    num2: number
  }
}>()

const bg = computed(() => {
  const dict = {
    'Big': 'bg-gradient-to-r from-[#1DBBDF] to-[#5087FF]',
    'Small': 'bg-gradient-to-r from-[#FCCB1A] to-[#FFA74D]'
  }
  return dict[type]
})
</script>

<template>
  <div class="box-border h-[3.8rem] text-white text-[1.2rem] rounded-[0.3rem] p-[0.1rem] border border-[#DEE2EA] w-[16.8rem]">
    <div class="h-full rounded-[0.3rem] flex justify-between items-center" :class="bg">
      <div class="ml-[0.8rem]">
        <p class="mb-0] leading-[1.3rem] font-bold">{{type}}</p>
        <p class="mb-0">2x</p>
      </div>
      <div class="h-[2.4rem] leading-[2.3rem] text-center w-[9rem] rounded-[1.2rem] bg-[rgba(0,0,0,0.5)] mr-[0.8rem]">
        <span class="text-[#19D573] font-bold after:content-['/'] after:font-normal" >{{info.num1}}</span>
        <span>{{info.num2}}k</span>
      </div>

    </div>
  </div>
</template>

<style scoped lang="scss">

</style>