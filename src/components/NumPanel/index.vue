<script setup lang="ts">
import {ref} from "vue";
import type {Ref} from 'vue'
import type {TopItemTp} from "@/components/NumPanel/index";

import TimeCard from "@/components/TimeCard/index.vue";
import TopItem from "./TopItem.vue";
import NumItem from './NumItem.vue';
import BottomItem from './BottomItem.vue';

defineOptions({
  name: 'NumPanel'
})

const numList = Array.from({length: 10}).map((_, index) => {
  const t = Math.ceil(Math.random() * 100);
  const y = t * 10;
  return {
    name: `${index}`,
    info: {
      num1: t,
      num2: 99,
      num3: y
    }
  }
})

const top_1: Ref<TopItemTp['item']> = ref({
  type: 'Green',
  multiple: 2,
  num1: 999.9,
  num2: 999.9
})
const top_2: Ref<TopItemTp['item']> = ref({
  type: 'Violet',
  multiple: 2,
  num1: 0,
  num2: 999.9
})
const top_3: Ref<TopItemTp['item']> = ref({
  type: 'Red',
  multiple: 2,
  num1: 100,
  num2: 999.9
})

const bottom_1_info = ref({
  num1: 999.9,
  num2: 999.9
})

const bottom_2_info = ref({
  num1: 0,
  num2: 100
})


</script>

<template>
  <section class="relative px-[0.8rem] py-[0.8rem] rounded-[0.8rem] text-xs overflow-hidden border border-theme-primary w-full bg-theme-bg">
    <div class="grid grid-cols-3 grid-rows-1 gap-6 mb-[0.8rem]">
      <TopItem :item="top_1"/>
      <TopItem :item="top_2"/>
      <TopItem :item="top_3"/>
    </div>
    <div class="grid grid-cols-5 gap-x-[0.2rem] gap-y-[0.8rem] mb-[0.8rem]">
      <NumItem v-for="(num, idx) in numList" :item="num" :key="num.name"/>
    </div>
    <div class="flex justify-between h-fit">
      <BottomItem type="Big" :info="bottom_1_info"/>
      <BottomItem type="Small" :info="bottom_2_info"/>
    </div>

    <div class="absolute inset-0 flex items-center justify-center bg-black/50">
      <div class="w-[26.4rem] h-[16.5rem] px-[2rem] py-[1.8rem] rounded-[1.6rem] border border-theme-primary bg-[#ebedf2] shadow-[0px_0px_0px_2px_#00A450_inset]">
        <div class="flex items-center size-full rounded-[0.8rem] bg-theme-white">
          <TimeCard class="w-[7.4rem]" :colors="['#14B567', '#1DDD7A']" />
        </div>
      </div>
    </div>
  </section>
</template>

<style scoped lang="scss">

</style>