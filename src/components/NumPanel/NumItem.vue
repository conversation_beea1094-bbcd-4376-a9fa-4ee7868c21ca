<script setup lang="ts">
import type {NumItemTp} from "./index";

const {
  item = {
    name:'',
    info: {
      num1: 0,
      num2: 0,
      num3: 0
    }
  }
} = defineProps<NumItemTp>()
defineOptions({
  name: 'NumPanel'
})
</script>

<template>
  <div class="border-solid border-[#DEE2EA] rounded-[0.4rem] border h-[7.7rem] bg-[#F2F3F5] py-[0.4rem]">
    <div class="border-b border-[#DEE2EA] h-[5.4rem] text-center pl-[0.3rem] pr-[0.5rem]">
      <img :src="`/images/num-circle-${item.name}.png`" class="h-[3.2rem] w-[3.2rem] m-auto" alt="number">
      <div class="flex justify-between align-bottom flex-none h-[1.5rem] mt-[0.3rem]">
        <div
            class="text-[#768096] w-2/3 text-[1rem]] whitespace-nowrap overflow-clip text-ellipsis leading-[1.9rem] text-left">
          {{item.info.num1}}K
        </div>
        <div class="text-xs text-[#1E2637] w-1/3 text-[1.2rem] text-right">{{ item.info.num2 }}x</div>
      </div>
    </div>
    <div class="text-[#2CCC78] font-bold text-center text-[1.2rem]">{{item.info.num3}}k</div>
  </div>
</template>

<style scoped lang="scss">

</style>