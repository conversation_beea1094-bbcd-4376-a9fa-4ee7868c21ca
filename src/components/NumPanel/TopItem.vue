<script setup lang="ts">
import type {TopItemTp} from "@/components/NumPanel/index";
import {computed} from "vue";
import {BackGroundDict} from "@/constant";

const {item} = defineProps<TopItemTp>();

const bg = computed(() => {
  return BackGroundDict[item.type]
})


</script>

<template>
  <div class="h-[4.4rem] border border-[#DEE2EA] rounded-[0.4rem] py-[0.1rem] px-[0.1rem]">
    <div class="h-full py-[0.4rem] px-[0.8rem] text-white rounded-[0.4rem]" :class="bg">
      <div class="flex justify-between text-[1.2rem] font-bold">
        <span>{{ item.type }}</span>
        <span>{{ item.multiple }}x</span>
      </div>
      <div class="h-[1.6rem] leading-[1.6rem] text-[1.2rem] bg-[rgba(0,0,0,0.4)] rounded-[1.2rem] flex">
        <span class="after:content-['/'] after:text-[#19D573] text-[#2CCC78] font-bold text-[1rem] text-right flex-1/2">{{ item.num1 }}K</span>
        <span class="text-white flex-1/2" >{{ item.num2 }}K</span>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>