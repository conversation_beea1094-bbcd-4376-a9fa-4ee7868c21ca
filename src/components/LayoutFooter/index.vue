<script setup lang="ts">

</script>

<template>
  <div class="footer-wrapper fixed bottom-0 w-full max-w-(--max-container) h-[8.8rem] rounded-t-2xl overflow-hidden pt-[2px]">
    <div class="flex bg-white rounded-t-2xl h-full p-[0.8rem]">
      <div class="flex flex-col justify-between">
        <div class="font-medium text-[1.4rem] text-theme-grey">Balance:</div>
        <div class="flex items-center gap-[0.4rem]">
          <span class="font-bold text-[1.2rem] text-theme-black">₹5218712.87</span>
          <svg class="size-[1.6rem] block cursor-pointer text-theme-primary" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7.80551 14.3557C4.12342 14.3557 1.12793 11.3587 1.12793 7.6748H2.60432C2.60432 10.5446 4.93764 12.8793 7.80551 12.8793C9.42252 12.8793 10.9202 12.1462 11.9142 10.8679L13.0797 11.7742C11.8038 13.4148 9.88148 14.3557 7.80551 14.3557Z" fill="currentColor"/>
            <path d="M1.86117 5.21973L2.8074 6.44246L3.75363 7.66537L1.87682 7.67732L0 7.68928L0.930586 6.45459L1.86117 5.21973Z" fill="currentColor"/>
            <path d="M14.6195 7.68092H13.1431C13.1431 4.81111 10.8098 2.47639 7.94193 2.47639C6.32492 2.47639 4.82727 3.20957 3.8334 4.48768L2.66797 3.58152C3.94344 1.94096 5.86578 1 7.94193 1C11.6238 1 14.6195 3.99707 14.6195 7.68092Z" fill="currentColor"/>
            <path d="M13.8866 10.1357L12.9404 8.91283L11.9941 7.6901L13.871 7.67797L15.7478 7.66602L14.8172 8.90088L13.8866 10.1357Z" fill="currentColor"/>
          </svg>
        </div>
        <button class="flex items-center gap-[0.4rem] h-[2.8rem] px-[0.8rem] cursor-pointer rounded-[0.4rem] bg-[#F2F3F5]">
          <svg class="size-[1.4rem] block" viewBox="0 0 14 13" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M3.05613 2.45548H8.67954C11.6175 2.45548 14 4.81597 14 7.72727C14 10.6395 11.6175 13 8.67954 13H1.58716C1.47092 13.0002 1.35577 12.9777 1.24829 12.9336C1.14082 12.8896 1.04313 12.8248 0.960802 12.7432C0.878477 12.6615 0.813134 12.5645 0.768508 12.4577C0.723883 12.3509 0.700851 12.2364 0.700728 12.1207C0.700728 11.636 1.09813 11.2424 1.58716 11.2424H8.67954C9.14439 11.2434 9.60489 11.1532 10.0347 10.9771C10.4645 10.801 10.8553 10.5423 11.1846 10.2159C11.514 9.88946 11.7755 9.50168 11.9542 9.07467C12.1329 8.64767 12.2253 8.18982 12.2262 7.72727C12.2252 7.2648 12.1327 6.80705 11.9539 6.38015C11.7752 5.95326 11.5137 5.56558 11.1843 5.23926C10.855 4.91293 10.4643 4.65435 10.0345 4.47827C9.60473 4.30219 9.14432 4.21207 8.67954 4.21305H2.99442L4.0183 5.22908C4.13175 5.3373 4.21404 5.47377 4.25662 5.62431C4.2992 5.77485 4.30052 5.93398 4.26045 6.0852C4.22038 6.23643 4.14038 6.37422 4.02875 6.48431C3.91711 6.59439 3.77791 6.67275 3.62558 6.71125C3.47338 6.75076 3.31338 6.74936 3.1619 6.70719C3.01043 6.66501 2.87293 6.58357 2.76346 6.4712L0.259383 3.98696C0.177168 3.90544 0.111934 3.80858 0.0674235 3.70192C0.0229125 3.59527 0 3.48092 0 3.36543C0 3.24995 0.0229125 3.1356 0.0674235 3.02894C0.111934 2.92228 0.177168 2.82542 0.259383 2.74391L2.76346 0.257808C2.93011 0.092698 3.15571 0 3.39088 0C3.62605 0 3.85165 0.092698 4.0183 0.257808C4.36427 0.600205 4.36427 1.1566 4.0183 1.49993L3.05519 2.45455L3.05613 2.45548Z" fill="#BBC0CB"/>
          </svg>
          <span class="font-medium text-[1.4rem] text-[#BBC0CB]">Revocation</span>
        </button>
      </div>
      <div class="flex items-center justify-end ml-auto gap-[0.8rem]">
        <div v-for="i in [1, 10, 100, 500, 1000]" :key="i" class="size-[4rem]">
          <img class="block size-full object-contain cursor-pointer" :src="`/images/chips-${i}.png`" alt="chips" />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>

.footer-wrapper {
  background-image: linear-gradient(90deg, #1EE37C 0%, #17CF6F 100%);
  box-shadow:
    0px -1px 6px 0px #00000040,
    0px 1px 0px 0px #19D573 inset;
}

</style>