<script setup lang="ts">
import { ref } from 'vue';

import RuleDialog from "@/components/RuleDialog/index.vue";
import TimeCard from "@/components/TimeCard/index.vue";
import TimeTabs from "./TimeTabs.vue";


const showRuleDialog = ref(false);
</script>

<template>
  <div class="h-[24rem] bg-[url('/images/header-bg.png')] bg-cover bg-center">
    <div class="grid grid-cols-[minmax(0,1fr)_auto_minmax(0,1fr)] h-[4.4rem] items-center">
      <div class="px-[1.6rem]"></div>
      <div>
        <img class="block h-[2.8rem]" src="/images/header-logo.png" alt="header logo" />
      </div>
      <div class="flex items-center justify-end px-[1.6rem] gap-[2.4rem]">
        <div>
          <img class="block h-[2.4rem] w-auto" src="/images/hongbao.png" />
        </div>
        <svg class="size-[2.4rem] text-theme-black cursor-pointer" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" @click="showRuleDialog = true">
          <path d="M22.2857 12C22.2857 6.31936 17.6806 1.71429 12 1.71429C6.31936 1.71429 1.71429 6.31936 1.71429 12C1.71429 17.6806 6.31936 22.2857 12 22.2857C17.6806 22.2857 22.2857 17.6806 22.2857 12ZM24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0C18.6274 0 24 5.37258 24 12Z" fill="currentColor"/>
          <path d="M10.7819 14.5033C10.7295 14.0524 10.761 13.658 10.8762 13.3199C11.002 12.9818 11.1749 12.6775 11.3949 12.407C11.6254 12.1365 11.8769 11.8886 12.1493 11.6632C12.4322 11.4378 12.6942 11.218 12.9352 11.0039C13.1866 10.7898 13.391 10.57 13.5481 10.3446C13.7053 10.1192 13.7839 9.87126 13.7839 9.60078C13.7839 9.26268 13.7053 8.97529 13.5481 8.73862C13.391 8.50195 13.1604 8.32163 12.8566 8.19766C12.5632 8.07369 12.2069 8.0117 11.7878 8.0117C11.3058 8.0117 10.8657 8.1244 10.4676 8.3498C10.0694 8.57521 9.67649 8.90767 9.2888 9.3472L8 8.06242C8.50295 7.44257 9.09496 6.94668 9.77603 6.57477C10.4676 6.19159 11.2272 6 12.055 6C12.8199 6 13.4957 6.12397 14.0825 6.37191C14.6798 6.61985 15.146 6.9974 15.4813 7.50455C15.8271 8.00043 16 8.62592 16 9.38101C16 9.79801 15.9162 10.1643 15.7485 10.4798C15.5914 10.7841 15.3818 11.0603 15.1198 11.3082C14.8684 11.5449 14.6012 11.7759 14.3183 12.0013C14.0354 12.2154 13.7734 12.4408 13.5324 12.6775C13.2914 12.9142 13.1028 13.179 12.9666 13.472C12.8304 13.7651 12.778 14.1088 12.8094 14.5033H10.7819ZM11.8035 19C11.4054 19 11.0753 18.8591 10.8134 18.5774C10.5514 18.2956 10.4204 17.9406 10.4204 17.5124C10.4204 17.0728 10.5514 16.7122 10.8134 16.4304C11.0753 16.1487 11.4054 16.0078 11.8035 16.0078C12.2017 16.0078 12.5318 16.1487 12.7937 16.4304C13.0557 16.7122 13.1866 17.0728 13.1866 17.5124C13.1866 17.9406 13.0557 18.2956 12.7937 18.5774C12.5318 18.8591 12.2017 19 11.8035 19Z" fill="currentColor"/>
        </svg>
      </div>
    </div>

    <div class="px-[0.8rem] mt-[1.2rem]">
      <TimeTabs />

      <div class="my-[0.8rem]">
        <div class="info-card bg-theme-linear-gradient grid grid-cols-2 gap-[1.2rem] h-[7.2rem] shadow rounded-[0.8rem]">
          <div class="flex flex-col items-start px-[1.4rem] py-[1.2rem] gap-[0.8rem] text-[1.2rem] font-medium text-theme-white">
            <div class="flex items-center justify-center bg-black/40 rounded-full px-[0.6rem] h-[2.4rem]">
              Win Go 30s
            </div>
            <div>25052305010420323</div>
          </div>
          <div class="py-[0.8rem] px-[1.6rem]">
            <div class="font-medium text-[1.2rem] text-theme-white mb-[0.4rem]">Time Remaining</div>
            <div class="flex gap-[0.2rem] items-center">
              <TimeCard class="flex-1 shrink-0" />
              <TimeCard class="flex-1 shrink-0" />
              <div class="text-[3rem] text-theme-white">:</div>
              <TimeCard class="flex-1 shrink-0" />
              <TimeCard class="flex-1 shrink-0" />
            </div>
          </div>
        </div>
      </div>

      <div class="h-[3.2rem] rounded-full p-[2px] bg-[linear-gradient(90deg,#1EE37C_0%,#17CF6F_100%)]">
        <div class="flex items-center gap-[0.8rem] size-full rounded-full bg-white py-[0.4rem] px-[0.6rem]">
          <img class="block h-full w-auto shrink-0" src="/images/notice.png" />
          <div class="text-theme-black font-medium text-[1rem] leading-[1.2rem] overflow-hidden text-ellipsis whitespace-nowrap">Stomershave good experience playing.. Stomershave good experience playing..</div>
        </div>
      </div>
    </div>
  </div>

  <RuleDialog v-model="showRuleDialog" />
</template>

<style lang="scss" scoped>
.info-card {
  mask-image:
    radial-gradient(circle at 50% 1.2rem, transparent 0, transparent 1.2rem, #fff 1.25rem),
    linear-gradient(
      to bottom,
      transparent 0,
      transparent 0.2rem,
      #fff 0.2rem,
      #fff 0.8rem,
      transparent 0.8rem,
      transparent 1rem
    );
  mask-size: 100%, 0.2rem 1rem;
  mask-position: 0 -1.2rem, center center;
  mask-repeat: repeat, repeat-y;
  mask-composite: subtract;
}
</style>