<script setup lang="ts">
import { ref } from 'vue';
import { TabGroup, TabList, Tab } from '@headlessui/vue'

const tabs = ['30s', '1Min', '3Min', '5Min'];

const activeIdx = ref(0);
</script>

<template>

  <TabGroup>
    <TabList class="flex h-[5.6rem] shadow rounded-[0.8rem] bg-white overflow-hidden">
      <Tab
        v-for="tab in tabs"
        v-slot="{ selected }"
        as="template"
        :key="tab"
      >
        <div
          :class="[
            'flex flex-1 shrink-0 items-center justify-center flex-col gap-[0.4rem] h-full rounded-[0.8rem]',
            'cursor-pointer outline-none',
            selected ? 'bg-theme-linear-gradient' : 'bg-theme-white'
          ]"
        >
          <img class="block h-[1.5rem] w-auto" alt="tab" :src="`/images/${tab.toLowerCase()}-${selected ? 'active' : 'default'}.png`" />
          <div :class="['text-[1.2rem]', selected ? 'text-theme-white font-bold' : 'text-theme-grey font-medium']">
            Win Go {{ tab }}
          </div>
        </div>
      </Tab>
    </TabList>
  </TabGroup>
</template>

<style lang="scss" scoped>

</style>