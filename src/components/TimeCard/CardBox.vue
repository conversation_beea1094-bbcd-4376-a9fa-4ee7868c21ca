<script setup lang="ts">
defineProps<{
  colors: string[]
}>();
</script>

<template>
  <svg class="absolute inset-0 w-full aspect-[74/96]" viewBox="0 0 74 96" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M65.4111 0C69.8294 0 73.411 3.58179 73.4111 8V32.4712C73.4111 34.81 71.5151 36.7061 69.1763 36.7061C66.8374 36.7061 64.9414 38.6021 64.9414 40.9409V55.0591C64.9414 57.3979 66.8374 59.2939 69.1763 59.2939C71.5151 59.2939 73.4111 61.19 73.4111 63.5288V88C73.4111 92.4183 69.8294 96 65.4111 96H8C3.58174 96 3.44309e-05 92.4183 0 88V63.5293C0 61.1902 1.89623 59.2939 4.23535 59.2939C6.57447 59.2939 8.4707 57.3977 8.4707 55.0586V40.9414C8.4707 38.6023 6.57447 36.7061 4.23535 36.7061C1.89623 36.7061 0 34.8098 0 32.4707V8C8.29114e-05 3.58179 3.58177 0 8 0H65.4111Z" fill="url(#paint0_linear_2732_13934)"/>
    <defs>
      <linearGradient id="paint0_linear_2732_13934" x1="40.4174" y1="96" x2="40.4174" y2="1.63438" gradientUnits="userSpaceOnUse">
        <stop offset="0" :stop-color="colors[0]"/>
        <stop offset="1" :stop-color="colors[1]"/>
      </linearGradient>

      <linearGradient id="paint0_linear_2732_13934" x1="40.4174" y1="96" x2="40.4174" y2="1.63438" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#14B567"></stop><stop offset="1" stop-color="#1DDD7A"></stop></linearGradient>
    </defs>
  </svg>
</template>

<style lang="scss" scoped>

</style>