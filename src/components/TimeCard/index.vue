<script setup lang="ts">
import { ref, watch } from 'vue';
import CardBox from './CardBox.vue';

withDefaults(defineProps<{
  colors?: string[]
  width?: number /** rem 单位 */
  height?: number /** rem 单位 */
}>(), {
  colors: () => ['#FFFFFF', '#FFFFFF'],
  width: 3,
  height: 4,
});

const hideNum = ref(4);
const showNum = ref(5);

watch(showNum, (newVal, oldVal) => {
  hideNum.value = oldVal;
  showNum.value = newVal;
});
</script>

<template>
  <div :key="showNum" class="time-card flex flex-col items-center justify-center aspect-[74/96]">
    <div class="time-card-rotated top-0">
      <CardBox :colors="colors" />
      <span class="time-card-text top-0">{{ showNum }}</span>
    </div>

    <div class="time-card-rotated bottom-0 bg-bottom">
      <CardBox :colors="colors" />
      <span class="time-card-text bottom-0">{{ hideNum }}</span>
    </div>

    <div class="time-card-hide time-card-rotated top-0">
      <CardBox :colors="colors" />
      <span class="time-card-text top-0">{{ hideNum }}</span>
    </div>

    <div class="time-card-show time-card-rotated top-0 bg-top">
      <CardBox :colors="colors" />
      <span class="time-card-text top-0 rotate-x-180">{{ showNum }}</span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@keyframes show {
  from { z-index: 0; }
  to { z-index: 1; transform: rotateX(-180deg); }
}

@keyframes hide {
  from { z-index: 1; }
  to { z-index: 0; transform: rotateX(180deg); }
}

.time-card {
  mask-image: linear-gradient(
    to bottom,
    #fff 0,
    #fff 1.9rem,
    transparent 1.9rem,
    transparent 2.1rem,
    #fff 2.1rem,
    #fff 4rem
  );
  mask-position: center;
  mask-repeat: no-repeat;
  transform-style: preserve-3d;

  .time-card-rotated {
    position: absolute;
    width: 100%;
    height: 50%;
    overflow: hidden;
    perspective: 200px;
    transform-origin: bottom;

    &.time-card-hide {
      z-index: 1;
      animation: hide 0.5s ease forwards;
    }

    &.time-card-show {
      z-index: 0;
      animation: show 0.5s ease forwards;
    }
  }

  .time-card-text {
    position: absolute;
    width: 100%;
    height: 200%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-theme-black);
    font-size: 2.4rem;
    font-weight: bold;
  }
}
</style>