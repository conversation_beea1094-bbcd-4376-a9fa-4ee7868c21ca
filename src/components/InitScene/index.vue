<script setup lang="ts">
import {watch} from "vue";
import {useAssetLoader} from "@/utils/AssetLoader.ts";

const emits = defineEmits<{
  (e: 'finish'): void
}>()

const { finish, progress, start } = useAssetLoader();
const publicReg = /^\/public/;

const images = Object.keys(import.meta.glob('/public/images/**')).map(path => path.replace(publicReg, ''));
const audios = Object.keys(import.meta.glob('/public/audio/**')).map(path => path.replace(publicReg, ''));

start({ images, audios })

watch(finish, (done) => done && emits('finish'));
</script>

<template>
<div class="loading-wrapper absolute inset-0 z-90 size-full bg-no-repeat bg-cover bg-center bg-[url('/images/startup-page-bg.jpg')]">
  <div class="absolute inset-x-0 w-[16rem] bottom-[4.8rem] mx-auto">
    <div class="relative w-full h-[0.8rem] bg-[#003015] rounded-full mb-[0.4rem]">
      <div :style="{ width: `${progress * 100}%` }" class="bg-theme-linear-gradient absolute inset-0 rounded-full"></div>
    </div>
    <div class="text-center text-[1.2rem] text-white font-medium">Loading... {{ Math.floor(progress * 100) }}%</div>
  </div>
</div>
</template>

<style lang="scss" scoped>

</style>