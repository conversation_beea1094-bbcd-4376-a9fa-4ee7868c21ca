<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from '@headlessui/vue'

const show = defineModel<boolean>('modelValue', { default: false });

function handleUpdateShow(newShow: boolean) {
  show.value = newShow;
}
</script>

<template>
  <TransitionRoot appear :show="show" as="template">
    <Dialog as="div" @close="handleUpdateShow(false)" class="relative z-90">
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black/70" />
      </TransitionChild>

      <div class="fixed inset-0 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4 text-center">
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel class="w-[32.7rem] transform overflow-hidden rounded-[0.8rem] bg-white text-left align-middle shadow-xl transition-all">
              <div class="relative h-[4rem] bg-theme-linear-gradient flex items-center justify-center">
                <DialogTitle as="h3" class="relative font-bold text-[1.4rem] px-[0.8rem] text-theme-white">
                  <span class="absolute inset-y-0 right-full m-auto h-[2px] w-[5rem] bg-[linear-gradient(to_right,transparent_0%,#FFFFFF_100%)]"></span>
                  <span>Rule</span>
                  <span class="absolute inset-y-0 left-full m-auto h-[2px] w-[5rem] bg-[linear-gradient(to_right,#FFFFFF_0%,transparent_100%)]"></span>
                </DialogTitle>
                <svg class="absolute inset-y-0 right-[1.2rem] m-auto size-[1.6rem] cursor-pointer" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" @click="handleUpdateShow(false)">
                  <rect x="0.00195312" y="3.03613" width="3.66676" height="18.3338" rx="1.82812" transform="rotate(-45 0.00195312 3.03613)" fill="white"/>
                  <rect x="12.9639" width="3.66676" height="18.3338" rx="1.82812" transform="rotate(45 12.9639 0)" fill="white"/>
                </svg>
              </div>
              <div class="p-[1.6rem] space-y-[0.8rem]">
                <h3 class="text-theme-black text-[1.4rem] font-bold pt-[0.8rem]">1、Introduction</h3>
                <p class="text-theme-grey text-[1.2rem] leading-[1.4rem] font-medium">Every 0.5 minutes，a draw is held; 2,800 draws are held each day. if you spend 100 to trade, after deducting a 2% service fee, the contract amount is 98.</p>
                
                <h3 class="text-theme-black text-[1.4rem] font-bold pt-[0.8rem]">2、Gameplay</h3>

                <p class="text-theme-primary text-[1.2rem] leading-[1.4rem] font-medium pt-[0.8rem]">“*Single Number:”</p>
                <p class="text-theme-grey text-[1.2rem] leading-[1.4rem] font-medium">Choose a single number to bet on. If the drawn number matches the bet number, It’s considered a win; otherwise, it’s a loss.</p>

                <p class="text-theme-primary text-[1.2rem] leading-[1.4rem] font-medium pt-[0.8rem]">“*Red:”</p>
                <p class="text-theme-grey text-[1.2rem] leading-[1.4rem] font-medium">Bet on red. If the drawn number is 1,3,5,7, or 9, it’s considered a win, otherwise, it’s a loss. If the drawn number is 5, the odds are 1.5.</p>

                <p class="text-theme-primary text-[1.2rem] leading-[1.4rem] font-medium pt-[0.8rem]">“*Purple:”</p>
                <p class="text-theme-grey text-[1.2rem] leading-[1.4rem] font-medium">Bet on purple. If the drawn number is 0 or 5, it’s considered a win; otherwise, it’s a loss.</p>

                <p class="text-theme-primary text-[1.2rem] leading-[1.4rem] font-medium pt-[0.8rem]">“*Big/small:”</p>
                <p class="text-theme-grey text-[1.2rem] leading-[1.4rem] font-medium">If the drawn number is greater than or equal to 5, it’sbig; if it’s less than or equal to 4, it’s small.</p>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<style lang="scss" scoped>

</style>