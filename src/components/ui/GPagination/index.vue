<script setup lang="ts">
import { computed } from 'vue';

const props = withDefaults(defineProps<{
  total?: number
  pageSize?: number
}>(), {
  total: 0,
  pageSize: 10
});

const emits = defineEmits<{
  (e: 'change', value: number): void
}>()

const curr = defineModel<number>('modelValue', { default: 1 });

const totalPage = computed(() => Math.ceil(props.total / props.pageSize));

function handlePrev() {
  if (curr.value <= 1) return;
  curr.value -= 1;
  emits('change', curr.value);
}

function handleNext() {
  if (curr.value >= totalPage.value) return;
  curr.value += 1;
  emits('change', curr.value);
}

</script>

<template>
  <div class="flex items-center justify-center h-[2rem] gap-[1.6rem]">
    <svg class="block size-[2rem] shrink-0" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" @click="handlePrev">
      <g v-if="curr <= 1" class="text-theme-grey">
        <path d="M4 0.5H16C17.933 0.5 19.5 2.067 19.5 4V16C19.5 17.933 17.933 19.5 16 19.5H4C2.067 19.5 0.5 17.933 0.5 16V4C0.5 2.067 2.067 0.5 4 0.5Z" stroke="currentColor" />
        <path d="M12.949 5.00813C12.5585 4.61772 11.9255 4.61774 11.535 5.00813L7.29181 9.2513C6.90173 9.64183 6.9015 10.275 7.29181 10.6654C7.29473 10.6683 7.29864 10.6703 7.30158 10.6732L11.536 14.9075C11.9265 15.2981 12.5595 15.2981 12.95 14.9075C13.3403 14.517 13.3405 13.8839 12.95 13.4935L9.41389 9.95735L12.949 6.42219C13.3395 6.03166 13.3395 5.39862 12.949 5.00813Z" fill="currentColor" />
      </g>
      <g v-else class="text-theme-primary">
        <path d="M0 4C0 1.79086 1.79086 0 4 0H16C18.2091 0 20 1.79086 20 4V16C20 18.2091 18.2091 20 16 20H4C1.79086 20 0 18.2091 0 16V4Z" fill="currentColor" />
        <path d="M12.949 5.00813C12.5585 4.61772 11.9255 4.61774 11.535 5.00813L7.29181 9.2513C6.90173 9.64183 6.9015 10.275 7.29181 10.6654C7.29473 10.6683 7.29864 10.6703 7.30158 10.6732L11.536 14.9075C11.9265 15.2981 12.5595 15.2981 12.95 14.9075C13.3403 14.517 13.3405 13.8839 12.95 13.4935L9.41389 9.95735L12.949 6.42219C13.3395 6.03166 13.3395 5.39862 12.949 5.00813Z" fill="white" />
      </g>
    </svg>

    <div class="text-theme-black text-[1.2rem] leading-[1.4rem] font-medium">
      {{ curr }} / {{ totalPage }}
    </div>

    <svg class="block size-[2rem] shrink-0" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" @click="handleNext">
      <g v-if="curr >= totalPage" class="text-theme-grey">
        <path d="M4 0.5H16C17.933 0.5 19.5 2.067 19.5 4V16C19.5 17.933 17.933 19.5 16 19.5H4C2.067 19.5 0.5 17.933 0.5 16V4C0.5 2.067 2.067 0.5 4 0.5Z" stroke="currentColor" />
        <path d="M7.70916 5.00813C8.09966 4.61772 8.73272 4.61774 9.12322 5.00813L13.3664 9.2513C13.7565 9.64183 13.7567 10.275 13.3664 10.6654C13.3635 10.6683 13.3596 10.6703 13.3566 10.6732L9.12225 14.9075C8.73172 15.2981 8.09871 15.2981 7.70819 14.9075C7.31789 14.517 7.31774 13.8839 7.70819 13.4935L11.2443 9.95735L7.70916 6.42219C7.31875 6.03166 7.31867 5.39862 7.70916 5.00813Z" fill="currentColor" />
      </g>
      <g v-else class="text-theme-primary">
        <path d="M0 4C0 1.79086 1.79086 0 4 0H16C18.2091 0 20 1.79086 20 4V16C20 18.2091 18.2091 20 16 20H4C1.79086 20 0 18.2091 0 16V4Z" fill="currentColor" />
        <path d="M7.70916 5.00813C8.09966 4.61772 8.73272 4.61774 9.12322 5.00813L13.3664 9.2513C13.7565 9.64183 13.7567 10.275 13.3664 10.6654C13.3635 10.6683 13.3596 10.6703 13.3566 10.6732L9.12225 14.9075C8.73172 15.2981 8.09871 15.2981 7.70819 14.9075C7.31789 14.517 7.31774 13.8839 7.70819 13.4935L11.2443 9.95735L7.70916 6.42219C7.31875 6.03166 7.31867 5.39862 7.70916 5.00813Z" fill="white" />
      </g>
    </svg>
  </div>
</template>

<style lang="scss" scoped>

</style>