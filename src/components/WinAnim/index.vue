<script setup lang="ts">
import { onMounted, ref, useTemplateRef } from 'vue';
import {SpinePlayer, type SpinePlayerConfig} from '@esotericsoftware/spine-player'

const animSize = 1356;

const winAnimRef = useTemplateRef('winAnimRef');

const showAnim = ref<'animation' | 'animation2' | ''>('');

const player = ref<SpinePlayer>();

onMounted(() => {
  setupSpineAnim();
});

function setupSpineAnim () {
  const config: SpinePlayerConfig = {
    alpha: true,
    atlasUrl: '/spine/wingo_youwin.atlas',
    jsonUrl: '/spine/wingo_youwin.json',
    defaultMix: 0.1,
    showLoading: false,
    showControls: false,
    preserveDrawingBuffer: true,
    animations: ['animation', 'animation2'],
    viewport: {
      x: animSize / 2 / 2 * -1,
      y: animSize / 2 / 2 / 2 * -1,
      width: animSize / 2,
      height: animSize / 2,
      padLeft: "0%",
      padRight: "0%",
      padTop: "0%",
      padBottom: "0%",
      // debugRender: true // Show the viewport bounds
    },
    success: (p) => {
      player.value = p;
      player.value.setAnimation('animation', false);
      // 获取 AnimationState
      const animationState = p.animationState;
      // 添加监听器来检测动画完成
      animationState?.addListener({
        complete: function () {
          showAnim.value = '';
        }
      });
    }
  }

  if (winAnimRef.value) {
    player.value = new SpinePlayer(winAnimRef.value as HTMLElement, config);
  }
}

function showResult () {
  showAnim.value = 'animation';
  player.value?.setAnimation('animation', false);
}

function showWin () {
  showAnim.value = 'animation2';
  player.value?.setAnimation('animation2', false);
}

window.showResult = showResult;
window.showWin = showWin;

</script>

<template>
  <div :class="['absolute inset-0 z-50 bg-black/50 transition-opacity duration-300', showAnim ? 'opacity-100' : 'opacity-0 pointer-events-none']">
    <div ref="winAnimRef" class="size-full relative">
      <div class="absolute inset-0 z-10 flex flex-col items-center justify-center">
        <div v-if="showAnim === 'animation'" class="relative w-full">
          <div class="absolute inset-x-0 m-auto -translate-y-[6.6rem] size-[4.5rem] result-ball">
            <img class="block size-full object-contain" src="/images/num-circle-4.png" />
          </div>

          <div class="absolute inset-x-0 translate-y-[3.5rem] text-center text-theme-black text-[1.4rem] font-medium result-text">
            Period
          </div>

          <div class="absolute inset-x-0 translate-y-[7.5rem] text-center text-theme-primary text-[1.2rem] font-bold result-text">
            25052305010420320
          </div>
        </div>

        <div v-if="showAnim === 'animation2'" class="relative w-full">
          <div class="absolute inset-x-0 translate-y-[3.6rem] text-center text-theme-yellow text-[1.4rem] font-bold result-text">
            You Win!
          </div>

          <div class="absolute inset-x-0 translate-y-[7.2rem] text-center text-theme-primary text-[2rem] font-bold result-text">
            ₹88.88
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>

@keyframes result-ball-anim {
  0% { transform: scale(0) rotate(0deg); }
  13% { transform: scale(3) rotate(360deg); }
  16% { transform: scale(1.8) rotate(360deg); }
  20% { transform: scale(2.2) rotate(360deg); }
  23% { transform: scale(1.9) rotate(360deg); }
  26% { transform: scale(2.0) rotate(360deg); }
  100% { transform: scale(2.0) rotate(360deg); }
}

@keyframes result-text-anim {
  0% { opacity: 0; transform: translateY(-6px); }
  33% { opacity: 0; transform: translateY(-6px); }
  100% { opacity: 1; transform: translateY(0); }
}

.result-ball {
  animation: result-ball-anim 2s ease forwards;
}

.result-text {
  animation: result-text-anim 2s ease forwards;
}

</style>