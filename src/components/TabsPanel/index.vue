<script setup lang="ts">
import { ref } from 'vue';
import { TabGroup, TabList, Tab, TabPanels, TabPanel } from '@headlessui/vue'
import GEmpty from "@/components/ui/GEmpty/index.vue";
import GPagination from "@/components/ui/GPagination/index.vue";
import PeriodList from "@/components/PeriodList/index.vue";


const tabs = ['Game History', 'Chart', 'Follow Strategy', 'My History'];

const curr = ref(3)
</script>

<template>
  <TabGroup>
    <TabList class="flex h-[4.8rem] gap-[0.8rem] px-[0.8rem] items-center rounded-t-[0.8rem] bg-contain bg-center bg-[url('/images/tabs-panel-bg.png')]">
      <Tab
        v-for="tab in tabs"
        v-slot="{ selected }"
        as="template"
        :key="tab"
      >
        <div
          :class="[
            'flex-1 shrink-0 flex items-center justify-center h-[3.6rem] px-[0.2rem] rounded-[0.8rem]',
            'text-center font-medium text-[1.2rem] leading-[1.4rem] outline-none',
            selected ? 'bg-theme-linear-gradient text-theme-white' : 'border-1 border-theme-grey text-theme-grey'
          ]"
        >
          {{ tab }}
        </div>
      </Tab>
    </TabList>
    <TabPanels>
      <TabPanel>
        <PeriodList/>
        <GPagination v-model="curr" :total="50" :page-size="10" />
      </TabPanel>

      <TabPanel>
        <GEmpty />
      </TabPanel>

      <TabPanel>
        <GEmpty />
      </TabPanel>

      <TabPanel>
        <GEmpty />
      </TabPanel>
    </TabPanels>
  </TabGroup>
</template>

<style lang="scss" scoped>

</style>