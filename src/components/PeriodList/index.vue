<script setup lang="ts">
import {computed} from "vue";
import {BackGroundDict} from "@/constant";
import type {PeriodItemTp} from "@/components/PeriodList/index";

defineOptions({
  name: 'PeriodList'
})

const list:Array<PeriodItemTp> = Array.from({length: 10}).map((_, index) => {
  const idn = Math.ceil(Math.random() * 1000000)
  return {
    num: index,
    id: `${idn}${idn}${idn}`,
    size: index % 2 > 0 ? 'Big' : 'Small',
    color: (index+1) % 3 === 0 ?
        ['Red', 'Violet'] :
        (index+1) % 2 > 0 ?
            ['Green'] :
            ['Red'],
  }
})

const periodList = computed<PeriodItemTp[]>(() => {
  return list.map(item => {
    const bg = item.color.map(a => {
      return BackGroundDict[a]
    })
    return {
      ...item,
      bg
    }
  })
})

</script>

<template>
  <section class="bg-[#F2F3F5] px-[0.8rem] pt-[0.4rem] h-[36rem] text-theme-black  rounded-b-[0.8rem] mb-[0.8rem]">
    <div
        class="bg-white flex w-full h-[3.2rem] items-center shadow-[0_0.1rem_0.2rem_0_rgba(0,0,0,0.2)] rounded-[0.4rem] text-[1.2rem] font-[500] mb-[0.2rem]">
      <span class="flex-1/5 text-center">Period</span>
      <span class="w-[7rem] text-center border-l-[0.2rem]">Number</span>
      <span class="w-[7rem] text-center border-l-[0.2rem]">Big/Small</span>
      <span class="w-[7rem] text-center border-l-[0.2rem]">Color</span>
    </div>
    <ul class="list-none">
      <li v-for="li in periodList" :key="li.id" class="flex items-center border-b border-[#DEE2EA] h-[3.2rem] text-[1rem] pl-[1rem] last:border-none">
        <div class="flex-1/5 text-center">{{ li.id }}</div>
        <div class="w-[7rem] text-center">
          <img :src="`/images/num-circle-${li.num}.png`" :alt="`number${li.num}`" class="h-[2rem] w-[2rem] m-auto">
        </div>
        <div class="w-[7rem] text-center">{{ li.size }}</div>
        <div class="w-[7rem] text-center space-y-[0.2rem]">
          <div v-for="(bg, bgIndex) in li.bg" :key="`${li.id}_${bgIndex}`" class="block w-[3rem] h-[1.2rem] rounded-[0.2rem] mx-auto" :class="`${bg}`"></div>
        </div>
      </li>
    </ul>
  </section>
</template>

<style scoped lang="scss">

</style>